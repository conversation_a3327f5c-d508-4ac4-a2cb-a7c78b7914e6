'use strict';

/**
 * TimescaleDB Migration Helper Utilities
 *
 * This module provides utility functions to handle TimescaleDB compression
 * during database migrations that need to modify table schemas.
 *
 * Usage:
 * const { withTimescaleDBCompression } = require('../utils/timescaledb-migration-helper');
 *
 * await withTimescaleDBCompression(queryInterface, transaction, 'table_name', async () => {
 *   // Your schema modification operations here
 *   await queryInterface.changeColumn('table_name', 'column', { ... }, { transaction });
 * });
 */

/**
 * Executes a callback function while temporarily disabling TimescaleDB compression
 * on the specified table if it exists and has compression enabled.
 *
 * @param {Object} queryInterface - Sequelize query interface
 * @param {Object} transaction - Database transaction
 * @param {string} tableName - Name of the table to check/modify
 * @param {Function} callback - Function to execute with compression disabled
 * @returns {Promise<void>}
 */
async function withTimescaleDBCompression(queryInterface, transaction, tableName, callback) {
  let hadCompression = false;
  let hadColumnstore = false;
  let hadCompressionEnabled = false;

  try {
    // 1️⃣ Check if compression is enabled at hypertable level
    const [compressionSettings] = await queryInterface.sequelize.query(`
      SELECT COUNT(*) as count
      FROM timescaledb_information.compression_settings
      WHERE hypertable_schema = 'public'
        AND hypertable_name = '${tableName}';
    `, { transaction });

    hadCompressionEnabled = compressionSettings.length > 0 && compressionSettings[0].count > 0;

    // 2️⃣ Detect compressed chunks
    const [compressedChunks] = await queryInterface.sequelize.query(`
      SELECT chunk_schema, chunk_name
      FROM timescaledb_information.chunks
      WHERE hypertable_schema = 'public'
        AND hypertable_name = '${tableName}'
        AND is_compressed = true;
    `, { transaction });

    hadCompression = compressedChunks.length > 0;

    // 3️⃣ Detect columnstore
    const [columnstoreEnabled] = await queryInterface.sequelize.query(`
      SELECT reloptions
      FROM pg_class pc
      JOIN pg_namespace pn ON pn.oid = pc.relnamespace
      WHERE pn.nspname = 'public'
        AND pc.relname = '${tableName}'
        AND reloptions @> ARRAY['timescaledb.columnstore=true'];
    `, { transaction });

    hadColumnstore = columnstoreEnabled.length > 0;

    // 4️⃣ If compression is enabled at hypertable level → disable it
    if (hadCompressionEnabled) {
      console.log(`🔧 Disabling compression for ${tableName} (hypertable level)...`);
      try {
        // First try to remove compression policy if it exists
        await queryInterface.sequelize.query(
          `SELECT remove_compression_policy('${tableName}');`,
          { transaction }
        );
        console.log(`📋 Removed compression policy for ${tableName}`);
      } catch (error) {
        console.log(`📋 No compression policy found for ${tableName} (this is normal)`);
      }

      // Then disable compression (without transaction as TimescaleDB operations may not work in transactions)
      await queryInterface.sequelize.query(
        `ALTER TABLE ${tableName} SET (timescaledb.compress = false);`
      );
    }

    // 5️⃣ If compression → decompress all chunks
    if (hadCompression) {
      console.log(`📦 Decompressing chunks for ${tableName}...`);
      for (const chunk of compressedChunks) {
        await queryInterface.sequelize.query(
          `SELECT decompress_chunk(format('%I.%I', '${chunk.chunk_schema}', '${chunk.chunk_name}'));`,
          { transaction }
        );
      }
    }

    // 6️⃣ If columnstore → rewrite chunks to rowstore, then disable columnstore
    if (hadColumnstore) {
      console.log(`📑 Rewriting columnstore chunks for ${tableName} to rowstore...`);

      const [columnstoreChunks] = await queryInterface.sequelize.query(`
        SELECT chunk_schema, chunk_name
        FROM timescaledb_information.chunks c
        JOIN pg_class pc ON pc.relname = c.chunk_name
        JOIN pg_namespace pn ON pn.nspname = c.chunk_schema AND pn.oid = pc.relnamespace
        WHERE c.hypertable_name = '${tableName}'
          AND c.hypertable_schema = 'public'
          AND pc.reloptions @> ARRAY['timescaledb.columnstore=true'];
      `, { transaction });

      for (const chunk of columnstoreChunks) {
        await queryInterface.sequelize.query(
          `ALTER TABLE "${chunk.chunk_schema}"."${chunk.chunk_name}" SET (timescaledb.columnstore = false);`,
          { transaction }
        );
      }

      console.log(`🚫 Disabling columnstore for ${tableName}...`);
      await queryInterface.sequelize.query(
        `ALTER TABLE ${tableName} SET (timescaledb.columnstore = false);`,
        { transaction }
      );
    }

    // 7️⃣ Run migration logic
    await callback(transaction);

    // 8️⃣ Restore columnstore if it was enabled
    if (hadColumnstore) {
      console.log(`🔄 Re-enabling columnstore for ${tableName}...`);
      await queryInterface.sequelize.query(
        `ALTER TABLE ${tableName} SET (timescaledb.columnstore = true);`,
        { transaction }
      );
    }

    // 9️⃣ Restore compression if it was enabled at hypertable level
    if (hadCompressionEnabled) {
      console.log(`🔄 Re-enabling compression for ${tableName} (hypertable level)...`);
      await queryInterface.sequelize.query(
        `ALTER TABLE ${tableName} SET (
          timescaledb.compress = true,
          timescaledb.compress_segmentby = 'id'
        );`
      );
    }

    // 🔟 Restore compression if it was enabled
    if (hadCompression) {
      console.log(`📦 Recompressing chunks for ${tableName}...`);
      const [chunks] = await queryInterface.sequelize.query(`
        SELECT chunk_schema, chunk_name
        FROM timescaledb_information.chunks
        WHERE hypertable_schema = 'public'
          AND hypertable_name = '${tableName}';
      `, { transaction });

      for (const chunk of chunks) {
        await queryInterface.sequelize.query(
          `SELECT compress_chunk(format('%I.%I', '${chunk.chunk_schema}', '${chunk.chunk_name}'));`,
          { transaction }
        );
      }
    }
  } catch (err) {
    console.error(`❌ Error handling TimescaleDB compression/columnstore for ${tableName}:`, err);
    throw err;
  }
}



/**
 * Executes a callback function while temporarily disabling TimescaleDB compression
 * on multiple tables if they exist and have compression enabled.
 *
 * @param {Object} queryInterface - Sequelize query interface
 * @param {Object} transaction - Database transaction
 * @param {string[]} tableNames - Array of table names to check/modify
 * @param {Function} callback - Function to execute with compression disabled
 * @returns {Promise<void>}
 */
async function withMultipleTimescaleDBCompression(queryInterface, transaction, tableNames, callback) {
  console.log(`🔧 Checking multiple tables for TimescaleDB compression: ${tableNames.join(', ')}`);

  const compressionStates = {};

  try {
    // Check if TimescaleDB extension exists
    const [extensionExists] = await queryInterface.sequelize.query(`
      SELECT EXISTS(SELECT 1 FROM pg_extension WHERE extname = 'timescaledb');
    `, { transaction });

    if (!extensionExists[0].exists) {
      console.log(`ℹ️  TimescaleDB extension not found, proceeding without compression handling`);
      await callback();
      return;
    }

    // Handle each table
    for (const tableName of tableNames) {
      compressionStates[tableName] = await disableCompressionIfEnabled(queryInterface, transaction, tableName);
    }

    // Execute the callback with compression disabled
    await callback();

    // Re-enable compression for tables that had it
    for (const [tableName, hadCompression] of Object.entries(compressionStates)) {
      if (hadCompression) {
        await reEnableCompression(queryInterface, transaction, tableName);
      }
    }

  } catch (error) {
    console.error(`❌ Error handling TimescaleDB compression for multiple tables:`, error.message);
    throw error;
  }
}

/**
 * Helper function to disable compression on a single table if enabled
 * @private
 */
async function disableCompressionIfEnabled(queryInterface, transaction, tableName) {
  // Check if table is a hypertable
  const [isHypertable] = await queryInterface.sequelize.query(`
    SELECT EXISTS (
      SELECT FROM timescaledb_information.hypertables
      WHERE hypertable_name = '${tableName}' AND hypertable_schema = 'public'
    );
  `, { transaction });

  if (!isHypertable[0].exists) {
    console.log(`ℹ️  ${tableName} is not a hypertable`);
    return false;
  }

  console.log(`✅ ${tableName} is a hypertable, checking compression...`);

  // Check if compression is enabled
  const [compressionEnabled] = await queryInterface.sequelize.query(`
    SELECT EXISTS (
      SELECT FROM timescaledb_information.compression_settings
      WHERE hypertable_name = '${tableName}' AND hypertable_schema = 'public'
    );
  `, { transaction });

  if (compressionEnabled[0].exists) {
    console.log(`⚠️  ${tableName} has compression enabled, temporarily disabling...`);

    // Decompress any compressed chunks first
    await queryInterface.sequelize.query(`
      SELECT decompress_chunk(chunk_name)
      FROM timescaledb_information.chunks
      WHERE hypertable_name = '${tableName}'
      AND hypertable_schema = 'public'
      AND is_compressed = true;
    `, { transaction });

    // Disable compression
    await queryInterface.sequelize.query(`
      ALTER TABLE ${tableName} SET (timescaledb.compress = false);
    `, { transaction });

    console.log(`✅ ${tableName} compression temporarily disabled`);
    return true;
  }

  return false;
}

/**
 * Helper function to re-enable compression on a table
 * @private
 */
async function reEnableCompression(queryInterface, transaction, tableName) {
  console.log(`🔄 Re-enabling compression for ${tableName}...`);
  await queryInterface.sequelize.query(`
    ALTER TABLE ${tableName} SET (
      timescaledb.compress = true,
      timescaledb.compress_segmentby = 'id'
    );
  `, { transaction });
  console.log(`✅ ${tableName} compression re-enabled`);
}

module.exports = {
  withTimescaleDBCompression,
  withMultipleTimescaleDBCompression
};
