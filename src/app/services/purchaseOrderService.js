class PurchaseOrderService {
  constructor({
    db,
    utils,
    fastify,
    constants,
    clientErrors,
    roleRepository,
    userRepository,
    requisitionRepository,
    canvassItemRepository,
    purchaseOrderRepository,
    invoiceReportRepository,
    purchaseOrderItemRepository,
    canvassRequisitionRepository,
    canvassItemSupplierRepository,
    purchaseOrderApproverRepository,
    purchaseOrderCancelledItemsRepository,
    supplierRepository,
    companyRepository,
    deliveryReceiptService,
    deliveryReceiptItemService,
    notificationService,
    rsPaymentRequestRepository,
    noteService,
    deliveryReceiptRepository,
    approverService,
    requisitionService,
  }) {
    this.db = db;
    this.Sequelize = db.Sequelize;
    this.utils = utils;
    this.fastify = fastify;
    this.constants = constants;
    this.clientErrors = clientErrors;
    this.roleRepository = roleRepository;
    this.userRepository = userRepository;
    this.requisitionRepository = requisitionRepository;
    this.canvassItemRepository = canvassItemRepository;
    this.invoiceReportRepository = invoiceReportRepository;
    this.purchaseOrderRepository = purchaseOrderRepository;
    this.purchaseOrderItemRepository = purchaseOrderItemRepository;
    this.canvassRequisitionRepository = canvassRequisitionRepository;
    this.canvassItemSupplierRepository = canvassItemSupplierRepository;
    this.purchaseOrderApproverRepository = purchaseOrderApproverRepository;
    this.purchaseOrderCancelledItemsRepository =
      purchaseOrderCancelledItemsRepository;
    this.supplierRepository = supplierRepository;
    this.companyRepository = companyRepository;
    this.deliveryReceiptService = deliveryReceiptService;
    this.deliveryReceiptItemService = deliveryReceiptItemService;
    this.notificationService = notificationService;
    this.rsPaymentRequestRepository = rsPaymentRequestRepository;
    this.noteService = noteService;
    this.deliveryReceiptRepository = deliveryReceiptRepository;
    this.approverService = approverService;
    this.requisitionService = requisitionService;
  }

  async #mapPurchaseOrderItems(purchaseOrderId) {
    const [purchaseOrderItems, deliveryItems] = await Promise.all([
      this.purchaseOrderItemRepository.findAll({
        where: { purchaseOrderId },
      }),
      this.deliveryReceiptItemService.getAllDeliveryReceiptItemsByPOId(
        purchaseOrderId,
      ),
    ]);

    const mappedQuantityDelivered = deliveryItems.data.reduce((acc, item) => {
      const { poItemId, qtyDelivered } = item;
      const delivered = parseFloat(qtyDelivered) || 0;
      acc[poItemId] = (acc[poItemId] || 0) + delivered;
      return acc;
    }, {});

    const purchaseOrderMappedItems = purchaseOrderItems.data.map((poItem) => {
      const {
        id: poItemId,
        purchaseOrderId,
        quantityPurchased,
        canvassItemId,
        canvassItemSupplierId,
      } = poItem;

      const purchased = parseFloat(quantityPurchased) || 0;
      const delivered = parseFloat(mappedQuantityDelivered[poItemId]) || 0;
      const quantityToCancel = +(purchased - delivered).toFixed(3);
      const quantityFulfilled = +purchased.toFixed(3) === +delivered.toFixed(3);

      return {
        poItemId,
        purchaseOrderId,
        quantityPurchased: +purchased.toFixed(3),
        quantityDelivered: +delivered.toFixed(3),
        quantityToCancel,
        quantityFulfilled,
        canvassItemId,
        canvassItemSupplierId,
      };
    });

    return purchaseOrderMappedItems;
  }

  #shouldAllowCancellationByPOStatus(purchaseOrderStatus) {
    const { PO_STATUS } = this.constants.purchaseOrder;

    return (
      purchaseOrderStatus === PO_STATUS.FOR_PO_APPROVAL ||
      purchaseOrderStatus === PO_STATUS.FOR_PO_REVIEW ||
      purchaseOrderStatus === PO_STATUS.FOR_SENDING
    );
  }

  async #shouldAllowCancellationByDeliveryStatus(purchaseOrderId) {
    const { DELIVERY_ITEM_STATUSES } = this.constants.deliveryReceiptItem;

    const deliveryReceipts =
      await this.deliveryReceiptService.getDeliveryReceiptsFromPurchaseOrderId(
        purchaseOrderId,
        {
          attributes: ['latest_delivery_status'],
        },
      );

    // IF PO HAS NO DELIVERY RECEIPT YET - ALLOW CANCELLATION
    if (!deliveryReceipts.total) return true;

    // IF PO IS FULLY DELIVERED - PREVENT CANCELLATION
    const isFullyDelivered = deliveryReceipts.data.some(
      (dr) =>
        dr.latest_delivery_status === DELIVERY_ITEM_STATUSES.FULLY_DELIVERED,
    );

    if (isFullyDelivered) return false;

    // IF ALL PO ITEMS ARE HAVE QTY FULLFILLED - PREVENT CANCELLATION
    const purchaseOrderMappedItems =
      await this.#mapPurchaseOrderItems(purchaseOrderId);

    const canCancelOnFullfilledQty = !purchaseOrderMappedItems.every(
      (item) => item.quantityFulfilled,
    );

    return canCancelOnFullfilledQty;
  }

  async getAllPurchaseOrders(payload = {}) {
    const {
      requisitionId,
      limit,
      page,
      sortBy = '{"createdAt": "desc"}',
      selection,
      search,
    } = payload;

    if (selection === 'invoice') {
      const purchaseOrderList = await this.purchaseOrderRepository.findAll({
        attributes: [
          'id',
          [
            this.Sequelize.fn(
              'CONCAT',
              'PO-',
              this.Sequelize.col('requisition.company_code'),
              this.Sequelize.col('po_letter'),
              this.Sequelize.col('po_number'),
            ),
            'poNumber',
          ],
        ],
        include: [
          {
            association: 'requisition',
            attributes: [],
          },
          {
            association: 'deliveryReceipts',
            attributes: [],
            required: true,
            where: {
              invoiceId: null,
              isDraft: false,
            },
          },
        ],
        where: { requisitionId, status: 'for_delivery' },
        order: [['poNumber', 'ASC']],
        subQuery: false,
        paginate: false,
      });
      return purchaseOrderList;
    } else {
      // Convert the parsedSortBy object into an array format for Sequelize ordering
      const order = Object.entries(JSON.parse(sortBy)).map(([key, value]) => {
        if (key === 'supplier') {
          // Use the literal directly in the ORDER BY clause
          return [
            this.db.Sequelize.literal(
              `(CASE
                WHEN purchase_orders.supplier_name IS NOT NULL 
                  THEN COALESCE(purchase_orders.supplier_name, 'N/A')
                ELSE (CASE purchase_orders.supplier_type 
                  WHEN 'company' THEN company.name
                  WHEN 'project' THEN project.name
                  WHEN 'supplier' THEN supplier.name
                END)
              END)`,
            ),
            value.toUpperCase(),
          ];
        }

        return [key, value.toUpperCase()];
      });

      // Build where clause with search functionality
      const whereClause = { requisitionId };

      if (search) {
        whereClause[this.db.Sequelize.Op.and] = [
          this.db.Sequelize.where(
            this.db.Sequelize.fn(
              'CONCAT',
              'PO-',
              this.db.Sequelize.col('requisition.company_code'),
              this.db.Sequelize.col('po_letter'),
              this.db.Sequelize.col('po_number'),
            ),
            { [this.db.Sequelize.Op.iLike]: `%${search}%` },
          ),
        ];
      }

      const purchaseOrderList = await this.purchaseOrderRepository.findAll({
        limit,
        page,
        order,
        where: whereClause,
        include: [
          {
            association: 'requisition',
            attributes: ['company_code'],
          },
          {
            association: 'supplier',
            attributes: ['id', 'name'],
          },
          {
            association: 'company',
            attributes: ['id', 'name'],
          },
          {
            association: 'project',
            attributes: ['id', 'name'],
          },
          {
            association: 'warranty',
            attributes: ['id', 'name'],
          },
          {
            required: false,
            association: 'purchaseOrderApprovers',
            attributes: ['id', 'userId', 'level', 'status', 'isAdhoc'],
            where: { status: 'approved' },
            order: [
              ['level', 'DESC'],
              ['isAdhoc', 'DESC'],
            ],
            limit: 1,
            include: [
              {
                association: 'approver',
                as: 'approver',
                attributes: ['id', 'firstName', 'lastName'],
              },
            ],
          },
        ],
        attributes: {
          exclude: ['supplierId', 'warrantyId', 'poNumber'],
          include: [
            [
              this.db.Sequelize.literal(
                `(CASE
                    WHEN purchase_orders.supplier_name IS NOT NULL 
                      THEN COALESCE(purchase_orders.supplier_name, 'N/A')
                    ELSE (CASE purchase_orders.supplier_type 
                      WHEN 'company' THEN company.name
                      WHEN 'project' THEN project.name
                      WHEN 'supplier' THEN supplier.name
                    END)
                  END)`,
              ),
              'supplierName',
            ],
            [
              this.db.Sequelize.literal(
                `(SELECT COALESCE(CONCAT(u.first_name, ' ', u.last_name), 'N/A') 
                  FROM purchase_order_approvers poa
                  LEFT JOIN users u ON poa.user_id = u.id
                  WHERE poa.purchase_order_id = purchase_orders.id
                  AND poa.status = 'approved'
                  ORDER BY poa.level DESC, poa.is_adhoc DESC
                  LIMIT 1)`,
              ),
              'lastApproverName',
            ],
            [
              this.Sequelize.fn(
                'CONCAT',
                'PO-',
                this.Sequelize.col('requisition.company_code'),
                this.Sequelize.col('po_letter'),
                this.Sequelize.col('po_number'),
              ),
              'poNumber',
            ],
          ],
        },
      });

      return purchaseOrderList;
    }
  }

  async getPODetails(purchaseOrderId) {
    const purchaseOrder = await this.purchaseOrderRepository.findOne({
      where: {
        id: purchaseOrderId,
      },
      include: [
        {
          association: 'requisition',
          attributes: ['id', 'dateRequired', 'deliveryAddress'],
        },
        {
          association: 'warranty',
          attributes: ['id', 'name'],
        },
        {
          association: 'requisition',
          attributes: ['company_code'],
        },
      ],
      attributes: {
        exclude: ['warrantyId', 'deliveryAddress', 'poNumber'],
        include: [
          [
            this.Sequelize.fn(
              'CONCAT',
              'PO-',
              this.Sequelize.col('requisition.company_code'),
              this.Sequelize.col('po_letter'),
              this.Sequelize.col('po_number'),
            ),
            'poNumber',
          ],
          'isNewDeliveryAddress',
          'newDeliveryAddress',
        ],
      },
    });

    if (!purchaseOrder) return null;

    let supplier;
    if (purchaseOrder.supplierType === 'supplier') {
      const result = await this.supplierRepository.findOne({
        attributes: ['id', 'name'],
        where: {
          id: purchaseOrder.supplierId,
        },
      });

      supplier = result;
    } else if (purchaseOrder.supplierType === 'company') {
      const result = await this.companyRepository.findOne({
        attributes: ['id', 'name'],
        where: {
          id: purchaseOrder.supplierId,
        },
      });

      supplier = result;
    }

    // Get the summary from purchaseOrderItemRepository to ensure consistent calculations
    const poSummary = await this.purchaseOrderItemRepository.getPOItemsSummary({
      purchaseOrderId,
    });

    // hijacked by the new purchase order totals
    // Update the purchase order object with consistent values
    purchaseOrder.totalAmount = poSummary.amount;
    purchaseOrder.totalDiscount = poSummary.discount;
    purchaseOrder.totalDiscountedAmount = poSummary.totalAmount;

    // identify deposit percentage using term type
    const termsResult = this.utils.Terms(purchaseOrder);

    const getDepositPercent = (terms) => {
      const TERMS = this.constants.purchaseOrder.TERMS;
      switch (terms) {
        case TERMS.DP_10:
        case TERMS.DP_10_RETENTION_10:
          return 10;
        case TERMS.DP_20:
        case TERMS.DP_20_RETENTION_10:
          return 20;
        case TERMS.DP_30:
        case TERMS.DP_30_RETENTION_10:
          return 30;
        case TERMS.DP_50:
          return 50;
        case TERMS.DP_80:
          return 80;
        default:
          return '0';
      }
    };

    const purchaseOrderTotals =
      await this.purchaseOrderRepository.getPurchaseOrderTotals(
        purchaseOrderId,
      );

    const depositPercent = getDepositPercent(purchaseOrder.terms);
    const depositAmount = termsResult?.remainingBalance
      ? parseFloat(purchaseOrderTotals.totalBaseAmount) -
        parseFloat(termsResult.remainingBalance)
      : 0;
    const remainingBalance = termsResult?.remainingBalance
      ? termsResult.remainingBalance
      : purchaseOrderTotals.totalBaseAmount;

    const canCancelPOByDeliveryStatus =
      await this.#shouldAllowCancellationByDeliveryStatus(purchaseOrderId);

    const canCancelPOByStatus = this.#shouldAllowCancellationByPOStatus(
      purchaseOrder.status,
    );

    const canCancelPO =
      (canCancelPOByDeliveryStatus || canCancelPOByStatus) &&
      !purchaseOrder.wasCancelled;

    const purchaseOrderAmountAgainstPaymentRequestMonitoring =
      await this.getPurchaseOrderAgainstPaymentRequestMonitoring(
        purchaseOrderId,
      );

    return {
      ...purchaseOrder,
      totalAmount: parseFloat(
        purchaseOrder.totalAmount + purchaseOrderTotals.totalAdditionalFees,
      ).toFixed(2),
      totalDiscount: parseFloat(
        purchaseOrderTotals.totalDiscountAmount,
      ).toFixed(2),
      totalDiscountedAmount: parseFloat(purchaseOrderTotals.grandTotal).toFixed(
        2,
      ),
      depositPercent,
      depositAmount,
      remainingBalance,
      supplier,
      canCancelPO,
      poAmountAndPrAmountMonitoring:
        purchaseOrderAmountAgainstPaymentRequestMonitoring,
    };
  }

  async getPurchaseOrderApprovers(purchaseOrderId) {
    const today = new Date(new Date().setHours(0, 0, 0, 0));

    const local = new Date(
      today.getTime() - today.getTimezoneOffset() * 60 * 1000,
    );

    const purchaseOrderApprovers =
      await this.purchaseOrderApproverRepository.findAll({
        where: {
          purchaseOrderId,
        },
        include: [
          {
            association: 'approver',
            attributes: ['id', 'firstName', 'lastName'],
            include: [
              {
                association: 'role',
                as: 'role',
                attributes: ['id', 'name'],
              },
              {
                model: this.db.leaveModel,
                attributes: ['id', 'startDate', 'endDate', 'totalDays'],
                as: 'userLeaves',
                include: {
                  model: this.db.userModel,
                  attributes: [
                    'id',
                    'firstName',
                    'lastName',
                    'username',
                    'email',
                  ],
                  include: [
                    {
                      model: this.db.roleModel,
                      attributes: ['id', 'name'],
                      as: 'role',
                    },
                  ],
                  as: 'poAltUser',
                },
                required: false,
                where: {
                  [this.Sequelize.Op.and]: [
                    {
                      startDate: {
                        [this.Sequelize.Op.lte]: local,
                      },
                    },
                    {
                      endDate: {
                        [this.Sequelize.Op.gte]: local,
                      },
                    },
                  ],
                },
              },
            ],
          },
          {
            model: this.db.userModel,
            as: 'altApprover',
            attributes: [
              'id',
              [
                this.Sequelize.fn(
                  'CONCAT',
                  this.Sequelize.col('altApprover.first_name'),
                  ' ',
                  this.Sequelize.col('altApprover.last_name'),
                ),
                'fullName',
              ],
            ],
            include: [
              { association: 'role', as: 'role', attributes: ['id', 'name'] },
            ],
          },
          {
            association: 'role',
            attributes: ['name'],
          },
        ],
        attributes: {
          exclude: ['userId'],
        },
      });

    return purchaseOrderApprovers;
  }

  async getPurchaseOrderAssignee(purchaseOrderId) {
    const existingPurchaseOrder = await this.purchaseOrderRepository.findOne({
      where: {
        id: purchaseOrderId,
      },
      attributes: ['assignedTo', 'requisitionId'],
    });

    if (!existingPurchaseOrder) {
      throw this.clientErrors.NOT_FOUND({
        message: `Purchase order not found with ID: ${purchaseOrderId}`,
      });
    }

    // if no assignee then return temporary placeholders
    if (!existingPurchaseOrder.assignedTo) {
      return {
        data: {},
      };
    }

    const existingRequisition = await this.requisitionRepository.findOne({
      where: {
        id: existingPurchaseOrder.requisitionId,
      },
      attributes: ['assignedTo'],
    });

    const mismatchAssignee =
      existingRequisition.assignedTo !== existingPurchaseOrder.assignedTo;
    if (mismatchAssignee) {
      this.fastify.log.info(
        `Updating "assigned to" of Purchase Order to match Requisitions, from ${existingPurchaseOrder.assignedTo} to ${existingRequisition.assignedTo}`,
      );
      await this.purchaseOrderRepository.update(
        { id: purchaseOrderId },
        {
          assignedTo: existingRequisition.assignedTo,
        },
      );
    }

    const assignedToDetails = await this.userRepository.findOne({
      where: {
        id: mismatchAssignee
          ? existingRequisition.assignedTo
          : existingPurchaseOrder.assignedTo,
      },
      attributes: ['id', 'firstName', 'lastName'],
      include: [
        {
          association: 'role',
          attributes: ['name'],
        },
      ],
    });

    return {
      data: {
        approver: {
          id: assignedToDetails.id,
          firstName: assignedToDetails.firstName,
          lastName: assignedToDetails.lastName,
        },
        ...assignedToDetails.role,
      },
    };
  }

  async getExistingPurchaseOrder(purchaseOrderId) {
    const existingPurchaseOrder = await this.purchaseOrderRepository.findOne({
      where: {
        id: purchaseOrderId,
      },
      include: [
        {
          association: 'canvassRequisition',
          attributes: ['id'],
          include: [
            {
              association: 'requisition',
              attributes: ['id', 'companyCode', 'assignedTo', 'createdBy'],
            },
          ],
        },
      ],
    });

    if (!existingPurchaseOrder) {
      throw this.clientErrors.NOT_FOUND({
        message: `Purchase order not found with ID: ${purchaseOrderId}`,
      });
    }

    return existingPurchaseOrder;
  }

  async createPurchaseOrder(payload = {}) {
    const { existingCanvass, transaction } = payload;
    const { PO_STATUS } = this.constants.purchaseOrder;
    const canvassItems = await this.canvassItemRepository.findAll({
      where: { canvassRequisitionId: existingCanvass.id },
      attributes: ['id'],
      paginate: false,
    });

    const selectedSuppliers =
      await this.canvassItemSupplierRepository.getSelectedSupplierByCanvassId(
        canvassItems?.data?.map((item) => item.id) || [],
      );

    if (!selectedSuppliers.total) {
      throw this.clientErrors.BAD_REQUEST({
        message: 'No selected suppliers found for this canvass requisition',
      });
    }

    const supplierGroups = this.#groupBySupplier(selectedSuppliers.data);
    for (const supplierGroup of supplierGroups) {
      try {
        await this.#createPOForSupplier({
          transaction,
          supplierGroup,
          requisition: existingCanvass.requisition,
          canvassRequisitionId: existingCanvass.id,
        });
      } catch (error) {
        this.fastify.log.error('[ERROR] Creating PO for supplier');
        this.fastify.log.error(error);
        throw error;
      }
    }
  }

  #groupBySupplier(selectedSuppliers) {
    const groups = new Map();

    for (const supplier of selectedSuppliers) {
      const key = `${supplier.supplierType}_${supplier.supplierId}_${supplier.term}`;

      if (!groups.has(key)) {
        groups.set(key, {
          supplierId: supplier.supplierId,
          supplierType: supplier.supplierType,
          term: supplier.term,
          items: [],
          totalAmount: 0,
          totalDiscount: 0,
          totalDiscountedAmount: 0,
        });
      }

      const group = groups.get(key);
      const itemAmount = supplier.unitPrice * supplier.quantity;
      let itemDiscount = supplier.discountValue ?? 0;

      if (supplier.discountType === 'percent') {
        itemDiscount = (supplier.discountValue / 100) * itemAmount;
      }

      group.totalAmount += itemAmount;
      group.totalDiscount += itemDiscount * supplier?.quantity;
      group.totalDiscountedAmount = group.totalAmount - group.totalDiscount;
      group.items.push(supplier);
    }

    return Array.from(groups.values());
  }

  async #createPOForSupplier(payload = {}) {
    const { transaction, requisition, supplierGroup, canvassRequisitionId } =
      payload;
    const { PO_STATUS } = this.constants.purchaseOrder;
    const [poIdentifier, poItems] = await Promise.all([
      this.#generatePONumberCode(transaction),
      Promise.resolve(
        supplierGroup.items.map((supplier) => ({
          canvassItemSupplierId: supplier.id,
          quantityPurchased: supplier.quantity,
          canvassItemId: supplier.canvassItemId,
          requisitionItemListId: supplier.canvassItem.requisitionItemListId,
        })),
      ),
    ]);

    const fixedTotalAmount = (supplierGroup.totalAmount ?? 0).toFixed(2);
    const fixedTotalDiscount = (supplierGroup.totalDiscount ?? 0).toFixed(2);
    const totalDiscountedAmount = fixedTotalAmount - fixedTotalDiscount;

    const purchaseOrder = await this.purchaseOrderRepository.create(
      {
        canvassRequisitionId,
        terms: supplierGroup.term,
        status: PO_STATUS.FOR_PO_REVIEW,
        requisitionId: requisition.id,
        poNumber: poIdentifier.poNumber,
        poLetter: poIdentifier.poLetter,
        supplierId: supplierGroup.supplierId,
        supplierType: supplierGroup.supplierType,
        totalAmount: fixedTotalAmount,
        totalDiscount: fixedTotalDiscount,
        totalDiscountedAmount: totalDiscountedAmount.toFixed(2),
        assignedTo: requisition.assignedTo,
        deliveryAddress: requisition.deliveryAddress,
      },
      { transaction },
    );

    const poItemsWithId = poItems.map((item) => ({
      ...item,
      purchaseOrderId: purchaseOrder.id,
    }));

    await Promise.all([
      this.purchaseOrderItemRepository.bulkCreate(poItemsWithId, {
        transaction,
      }),
      this.#generatePOApprovers({
        requisition,
        transaction,
        purchaseOrderId: purchaseOrder.id,
      }),
    ]);
  }

  async #generatePOApprovers({ purchaseOrderId, requisition, transaction }) {
    const { USER_TYPES } = this.constants.user;
    const [supervisorRole, purchasingHeadRole, assignedUser] =
      await Promise.all([
        this.roleRepository.findOne({
          where: { name: USER_TYPES.SUPERVISOR },
        }),
        this.roleRepository.findOne({
          where: { name: USER_TYPES.PURCHASING_HEAD },
        }),
        this.userRepository.getUserById(requisition.assignedTo, {
          paranoid: false,
        }),
      ]);

    if (!supervisorRole || !purchasingHeadRole) {
      throw this.clientErrors.BAD_REQUEST({
        message: 'Required approver roles for purchase order are not set',
      });
    }

    const userPurchaseHead = await this.userRepository.findOne({
      where: { roleId: purchasingHeadRole.id },
    });

    const approvers = [
      {
        level: 1,
        roleId: supervisorRole.id,
        purchaseOrderId,
        ...(assignedUser?.supervisor?.id && {
          userId: assignedUser.supervisor.id,
        }),
      },
      {
        level: 2,
        roleId: purchasingHeadRole.id,
        purchaseOrderId,
        ...(userPurchaseHead?.id && {
          userId: userPurchaseHead.id,
        }),
      },
    ];

    await this.purchaseOrderApproverRepository.bulkCreate(approvers, {
      transaction,
    });
  }

  async #generatePONumberCode(transaction) {
    const lastPurchaseOrder = await this.purchaseOrderRepository.findOne({
      order: [
        ['poLetter', 'DESC'],
        ['poNumber', 'DESC'],
      ],
      transaction,
      lock: true,
    });

    const { nextLetter, nextNumber } = this.utils.getNextNumberAndLetter(
      lastPurchaseOrder?.poNumber,
      lastPurchaseOrder?.poLetter,
    );

    return {
      poNumber: nextNumber,
      poLetter: nextLetter,
    };
  }

  async updatePurchaseOrder({ purchaseOrderId, body, transaction }) {
    const updateResults = await Promise.all(
      body.map(async (item) => {
        try {
          const quantityRequested =
            await this.purchaseOrderItemRepository.findOne({
              where: { purchaseOrderId },
              include: [
                {
                  association: 'canvassItemSupplier',
                  attributes: ['quantity'],
                },
              ],
            });

          if (
            item.quantityPurchased >
            quantityRequested.canvassItemSupplier.quantity
          ) {
            this.fastify.log
              .error(`QUANTITY_PURCHASED_IS_GREATER_THAN_REQUESTED:
              ${item.quantityPurchased} > ${quantityRequested.canvassItemSupplier.quantity}
              for purchase order item ${item.purchaseOrderItemId}
              `);
            throw this.clientErrors.BAD_REQUEST({
              message: `Quantity purchased is greater than the quantity requested`,
            });
          }

          return await this.purchaseOrderItemRepository.update(
            { id: item.purchaseOrderItemId, purchaseOrderId },
            { quantityPurchased: item.quantityPurchased },
            { transaction },
          );
        } catch (error) {
          this.fastify.log.error(
            `Failed to update item ${item.purchaseOrderItemId}:`,
            error,
          );
          throw error;
        }
      }),
    );

    const failedUpdates = updateResults.filter((result) => result === null);
    if (failedUpdates.length > 0) {
      return false;
    }

    return true;
  }

  async submitPurchaseOrder(
    {
      purchaseOrderId,
      userFromToken,
      warrantyId,
      isNewDeliveryAddress,
      newDeliveryAddress,
      addedDiscount,
      isAddedDiscountFixedAmount,
      isAddedDiscountPercentage,
    },
    transaction,
  ) {
    const { PO_STATUS } = this.constants.purchaseOrder;

    const existingPurchaseOrder =
      await this.purchaseOrderRepository.getById(purchaseOrderId);

    if (!existingPurchaseOrder) {
      throw this.clientErrors.NOT_FOUND({
        message: `Purchase Order with ID ${purchaseOrderId} not found`,
      });
    }

    // Check if user is authorized to submit the PO
    if (existingPurchaseOrder.assignedTo !== userFromToken?.id) {
      throw this.clientErrors.FORBIDDEN({
        message:
          'Only the assigned purchasing staff can submit this purchase order',
      });
    }

    // validate added discount should be less than purchase order's total amount
    const { totalAmount: purchaseOrderAmount, ...rest } =
      await this.purchaseOrderItemRepository.getPOItemsSummary({
        purchaseOrderId,
      });

    let purchaseOrderDiscount = 0;
    if (addedDiscount > 0 && isAddedDiscountFixedAmount) {
      purchaseOrderDiscount = parseFloat(addedDiscount).toFixed(2);
    } else if (addedDiscount > 0 && isAddedDiscountPercentage) {
      purchaseOrderDiscount =
        (parseFloat(addedDiscount).toFixed(2) / 100) * purchaseOrderAmount;
    }

    if (parseFloat(purchaseOrderDiscount) > parseFloat(purchaseOrderAmount)) {
      throw this.clientErrors.BAD_REQUEST({
        message: `Added discount value should not be larger than purchase order total amount. (₱ ${purchaseOrderAmount})`,
      });
    }

    if (existingPurchaseOrder.status === PO_STATUS.FOR_PO_APPROVAL) {
      throw this.clientErrors.BAD_REQUEST({
        message: 'Purchase order already submitted',
      });
    }

    return await this.purchaseOrderRepository.update(
      { id: purchaseOrderId },
      {
        warrantyId: warrantyId || null,
        isNewDeliveryAddress,
        totalAmount: rest.amount,
        totalDiscount: parseFloat(rest.discount).toFixed(2),
        totalDiscountedAmount: purchaseOrderAmount - purchaseOrderDiscount,
        newDeliveryAddress,
        addedDiscount,
        isAddedDiscountFixedAmount,
        isAddedDiscountPercentage,
      },
      { transaction },
    );
  }

  async cancelPurchaseOrder(poId, transaction) {
    const { PO_STATUS } = this.constants.purchaseOrder;

    const purchaseOrderId = parseInt(poId);
    const existingPurchaseOrder =
      await this.purchaseOrderRepository.getById(purchaseOrderId);

    if (!existingPurchaseOrder) {
      throw this.clientErrors.NOT_FOUND({
        message: `Purchase Order with ID ${purchaseOrderId} not found`,
      });
    }

    if (
      existingPurchaseOrder.status === PO_STATUS.CANCELLED_PO ||
      existingPurchaseOrder.wasCancelled
    ) {
      throw this.clientErrors.BAD_REQUEST({
        message: `Purchase order with ID ${purchaseOrderId} has already been cancelled.`,
      });
    }

    const canCancelPOByDeliveryStatus =
      await this.#shouldAllowCancellationByDeliveryStatus(purchaseOrderId);

    const canCancelPOByStatus = this.#shouldAllowCancellationByPOStatus(
      existingPurchaseOrder.status,
    );

    if (!(canCancelPOByStatus || canCancelPOByDeliveryStatus)) {
      throw this.clientErrors.BAD_REQUEST({
        message: `Purchase order cannot be cancelled current status`,
      });
    }

    const purchaseOrderData =
      await this.purchaseOrderItemRepository.getPOItemsById({
        purchaseOrderId: existingPurchaseOrder.id,
      });

    if (!purchaseOrderData.total) {
      return;
    }

    // INCREMENT "CANCELLED QTY" BASED ON DR FULFILLMENT
    const mappedPurchaseOrderItems =
      await this.#mapPurchaseOrderItems(purchaseOrderId);

    const toCancelCanvassItems = mappedPurchaseOrderItems.filter(
      (poItem) => !poItem.quantityFulfilled,
    );

    const hasAtLeastOneDelivered = mappedPurchaseOrderItems.some(
      (poItem) => poItem.quantityDelivered > 0,
    );

    const poStatus = hasAtLeastOneDelivered
      ? PO_STATUS.FOR_DELIVERY
      : PO_STATUS.CANCELLED_PO;

    for (const cancelledItem of toCancelCanvassItems) {
      await this.canvassItemRepository.update(
        { id: cancelledItem.canvassItemId },
        {
          cancelledQty: cancelledItem.quantityToCancel,
        },
        { transaction },
      );
    }

    return await this.purchaseOrderRepository.update(
      { id: purchaseOrderId },
      { status: poStatus, wasCancelled: true },
      { transaction },
    );
  }

  async approvePurchaseOrder(payload = {}) {
    const { existingPurchaseOrder, approver, transaction } = payload;

    const { PO_STATUS, PO_APPROVER_STATUS } = this.constants.purchaseOrder;

    const forApprovalStatuses = [
      PO_STATUS.FOR_PO_APPROVAL,
      PO_STATUS.PO_REJECTED,
    ];

    const isReadyForApproval = !forApprovalStatuses.includes(
      existingPurchaseOrder.status,
    );

    if (isReadyForApproval) {
      throw this.clientErrors.BAD_REQUEST({
        message: `Purchase order sheet is not for approval`,
      });
    }

    const approvers = await this.purchaseOrderApproverRepository.findAll({
      where: { purchaseOrderId: existingPurchaseOrder.id },
      paginate: false,
      order: [
        ['level', 'ASC'],
        ['isAdhoc', 'ASC'],
      ],
    });

    const currentApproverIndex = approvers.data.findIndex((approverRecord) => {
      const isApprover =
        approverRecord.userId === approver.id ||
        approverRecord.altApproverId === approver.id;

      return isApprover;
    });

    if (currentApproverIndex === -1) {
      throw this.clientErrors.FORBIDDEN({
        message: `You are not authorized to approve this purchase order`,
      });
    }

    const currentApprover = approvers.data[currentApproverIndex];

    if (currentApprover.status === PO_APPROVER_STATUS.APPROVED) {
      throw this.clientErrors.BAD_REQUEST({
        message: `You already approved this purchase order`,
      });
    }

    // if (currentApprover.isAdhoc) {
    //   const primaryApprover = approvers.data.find(
    //     (approver) =>
    //       approver.level === currentApprover.level && !approver.isAdhoc,
    //   );

    //   if (primaryApprover?.status !== PO_APPROVER_STATUS.APPROVED) {
    //     throw this.clientErrors.BAD_REQUEST({
    //       message: `Level ${currentApprover.level} primary approver must approve first`,
    //     });
    //   }
    // }

    await this.approverService.overrideApprover({
      model: 'purchaseOrder',
      modelId: existingPurchaseOrder.id,
      approverId: approver.id,
      status: 'approved',
      transaction,
      requisitionId: existingPurchaseOrder.requisitionId,
    });

    await this.purchaseOrderApproverRepository.update(
      {
        userId: currentApprover.userId,
        purchaseOrderId: existingPurchaseOrder.id,
      },
      { status: PO_APPROVER_STATUS.APPROVED },
      { transaction },
    );

    const updatedApprovers = await this.purchaseOrderApproverRepository.findAll(
      {
        where: { purchaseOrderId: existingPurchaseOrder.id },
        transaction,
        paginate: false,
        order: [
          ['level', 'ASC'],
          ['isAdhoc', 'ASC'],
        ],
      },
    );

    const allApproved = updatedApprovers.data.every(
      (approver) => approver.status === PO_APPROVER_STATUS.APPROVED,
    );

    if (allApproved) {
      await this.purchaseOrderRepository.update(
        { id: existingPurchaseOrder.id },
        { status: PO_STATUS.FOR_SENDING },
        { transaction },
      );
    }

    return allApproved;
  }
  async updatePurchaseOrderForDelivery(purchaseOrderId, userId, transaction) {
    const { PO_STATUS, PO_APPROVER_STATUS } = this.constants.purchaseOrder;

    // Get the purchase order
    const existingPurchaseOrder = await this.purchaseOrderRepository.getById(
      parseInt(purchaseOrderId),
    );

    if (!existingPurchaseOrder) {
      throw this.clientErrors.NOT_FOUND({
        message: `Purchase Order with ID ${purchaseOrderId} not found`,
      });
    }

    // Verify the logged user is the assignedTo user
    if (existingPurchaseOrder.assignedTo !== userId) {
      throw this.clientErrors.FORBIDDEN({
        message: `Only the assigned purchasing staff can update this purchase order to "for delivery"`,
      });
    }

    // Verify the purchase order is in FOR_SENDING status
    if (existingPurchaseOrder.status !== PO_STATUS.FOR_SENDING) {
      throw this.clientErrors.BAD_REQUEST({
        message: `Purchase order must be in "for sending" status to update to "for delivery"`,
      });
    }

    // Verify all approvers have approved the purchase order
    const approvers = await this.purchaseOrderApproverRepository.findAll({
      where: { purchaseOrderId: existingPurchaseOrder.id },
      paginate: false,
    });

    const allApproved = approvers.data.every(
      (approver) => approver.status === PO_APPROVER_STATUS.APPROVED,
    );

    if (!allApproved) {
      throw this.clientErrors.BAD_REQUEST({
        message: `All approvers must approve the purchase order before updating to "for delivery"`,
      });
    }

    // Update purchase order status to FOR_DELIVERY
    await this.purchaseOrderRepository.update(
      { id: purchaseOrderId },
      { status: PO_STATUS.FOR_DELIVERY },
      { transaction },
    );

    // Update requisition status to FOR_DELIVERY
    // await this.requisitionRepository.update(
    //   { id: existingPurchaseOrder.requisitionId },
    //   { status: PO_STATUS.FOR_DELIVERY },
    //   { transaction },
    // );

    return true;
  }

  async rejectPurchaseOrder(payload = {}) {
    const { existingPurchaseOrder, approverId, transaction } = payload;
    const { PO_STATUS, PO_APPROVER_STATUS } = this.constants.purchaseOrder;

    const approvers = await this.purchaseOrderApproverRepository.findAll({
      where: { purchaseOrderId: existingPurchaseOrder.id },
      paginate: false,
      order: [
        ['level', 'ASC'],
        ['isAdhoc', 'ASC'],
      ],
    });

    const currentApprover = approvers.data.find(
      (approver) => approver.userId === approverId,
    );

    if (currentApprover.status === PO_APPROVER_STATUS.REJECTED) {
      return;
    }

    if (currentApprover.status === PO_APPROVER_STATUS.APPROVED) {
      throw this.clientErrors.BAD_REQUEST({
        message: 'Cannot reject purchase order after approval',
      });
    }

    // if (currentApprover.isAdhoc) {
    //   const primaryApprover = approvers.data.find(
    //     (approver) =>
    //       approver.level === currentApprover.level && !approver.isAdhoc,
    //   );

    //   if (primaryApprover?.status !== PO_APPROVER_STATUS.APPROVED) {
    //     throw this.clientErrors.BAD_REQUEST({
    //       message: `Level ${currentApprover.level} primary approver must approve first`,
    //     });
    //   }
    // }

    await this.approverService.overrideApprover({
      model: 'purchaseOrder',
      modelId: existingPurchaseOrder.id,
      approverId,
      status: 'rejected',
      transaction,
      requisitionId: existingPurchaseOrder.requisitionId,
    });

    await this.purchaseOrderApproverRepository.update(
      {
        userId: currentApprover.userId,
        purchaseOrderId: existingPurchaseOrder.id,
      },
      { status: PO_APPROVER_STATUS.REJECTED },
      { transaction },
    );

    await this.purchaseOrderRepository.update(
      { id: existingPurchaseOrder.id },
      { status: PO_STATUS.PO_REJECTED },
      { transaction },
    );
  }

  async addPurchaseOrderAdhocApprover(payload = {}) {
    const {
      purchaseOrderId,
      approver,
      creatorId,
      requisitionId,
      defaultApproverName,
      canvassRequisitionId,
      transaction,
    } = payload;
    const { PO_APPROVER_STATUS } = this.constants.purchaseOrder;
    const { NOTIFICATION_TYPES, NOTIFICATION_DETAILS } =
      this.constants.notification;

    const approvers = await this.purchaseOrderApproverRepository.findAll({
      where: { userId: creatorId, purchaseOrderId },
      paginate: false,
      order: [
        ['level', 'ASC'],
        ['isAdhoc', 'ASC'],
      ],
    });

    if (!approvers.data.length) {
      throw this.clientErrors.FORBIDDEN({
        message: 'Only existing approvers can add adhoc approvers',
      });
    }

    const creatorApprover = approvers.data[0];

    if (creatorApprover.isAdhoc) {
      throw this.clientErrors.BAD_REQUEST({
        message: 'Adhoc approvers cannot add other adhoc approvers',
      });
    }

    const isExistingApprover =
      await this.purchaseOrderApproverRepository.findOne({
        where: {
          purchaseOrderId,
          userId: approver.id,
        },
      });

    if (isExistingApprover) {
      throw this.clientErrors.BAD_REQUEST({
        message: 'Approver already exists',
      });
    }

    const existingAdhoc = await this.purchaseOrderApproverRepository.findOne({
      where: {
        purchaseOrderId,
        level: creatorApprover.level,
        isAdhoc: true,
      },
    });

    if (existingAdhoc) {
      await this.purchaseOrderApproverRepository.update(
        { id: existingAdhoc.id },
        {
          userId: approver.id,
          addedBy: creatorId,
        },
        { transaction },
      );

      return;
    }

    await this.purchaseOrderApproverRepository.create(
      {
        purchaseOrderId,
        userId: approver.id,
        level: creatorApprover.level,
        isAdhoc: true,
        roleId: approver.role.id,
        addedBy: creatorId,
      },
      { transaction },
    );

    await this.notificationService.sendNotification({
      transaction,
      senderId: creatorId,
      type: NOTIFICATION_TYPES.PURCHASE_ORDER,
      title: NOTIFICATION_DETAILS.PURCHASE_ORDER_ADDITIONAL_APPROVER.title,
      message:
        NOTIFICATION_DETAILS.PURCHASE_ORDER_ADDITIONAL_APPROVER.message(
          defaultApproverName,
        ),
      recipientUserIds: [approver.id],
      metaData: {
        addedBy: creatorId,
        adhocApprover: approver.id,
        purchaseOrderId,
        requisitionId,
        canvassRequisitionId,
      },
    });
  }

  async removePurchaseOrderAdhocApprover(payload = {}) {
    const { purchaseOrderId, primaryApproverId } = payload;
    const { PO_APPROVER_STATUS } = this.constants.purchaseOrder;

    const approvers = await this.purchaseOrderApproverRepository.findAll({
      paginate: false,
      where: {
        purchaseOrderId,
      },
    });

    const primaryApprover = approvers.data.find(
      (approver) => approver.userId === primaryApproverId && !approver.isAdhoc,
    );

    if (!primaryApprover) {
      throw this.clientErrors.FORBIDDEN({
        message: 'You are not authorized to remove adhoc approvers',
      });
    }

    const adhocApprover = approvers.data.find(
      (approver) =>
        approver.isAdhoc && approver.level === primaryApprover.level,
    );

    if (!adhocApprover) {
      throw this.clientErrors.NOT_FOUND({
        message: `No adhoc approver found for level ${primaryApprover.level}`,
      });
    }

    await this.purchaseOrderApproverRepository.destroy({
      id: adhocApprover.id,
    });
  }

  async getAllAllowedPOAdhocApprovers() {
    const { USER_TYPES } = this.constants.user;

    const allowablePOAdhocApproversList = await this.userRepository.findAll({
      where: {
        status: 'active',
        deletedAt: null,
      },
      include: [
        {
          association: 'role',
          attributes: ['id', 'name'],
          where: {
            name: {
              [this.db.Sequelize.Op.in]: [
                USER_TYPES.SUPERVISOR,
                USER_TYPES.ASSISTANT_MANAGER,
                USER_TYPES.DEPARTMENT_HEAD,
                USER_TYPES.DEPARTMENT_DIVISION_HEAD,
                USER_TYPES.AREA_STAFF,
              ],
            },
          },
        },
      ],
      attributes: {
        exclude: [
          'password',
          'tempPass',
          'otpSecret',
          'isPasswordTemporary',
          'supervisorId',
          'createdAt',
          'updatedAt',
          'deletedAt',
        ],
      },
    });

    return allowablePOAdhocApproversList;
  }

  async resubmitRejectedPurchaseOrder(payload = {}) {
    const { purchaseOrderId, transaction } = payload;

    const existingPurchaseOrder = await this.purchaseOrderRepository.findOne({
      attributes: ['id'],
      where: {
        id: purchaseOrderId,
      },
    });

    if (!existingPurchaseOrder) {
      throw this.clientErrors.NOT_FOUND({
        message: 'Purchase order not found',
      });
    }

    const { PO_APPROVER_STATUS } = this.constants.purchaseOrder;

    await this.purchaseOrderApproverRepository.update(
      {
        purchaseOrderId: existingPurchaseOrder.id,
        status: PO_APPROVER_STATUS.REJECTED,
      },
      { status: PO_APPROVER_STATUS.PENDING, overrideBy: null },
      { transaction },
    );

    return;
  }

  async getPODetailsSupplierWarranty(purchaseOrderId) {
    const purchaseOrder = await this.purchaseOrderRepository.findOne({
      where: {
        id: purchaseOrderId,
      },
      include: [
        {
          association: 'supplier',
        },
        {
          association: 'warranty',
        },
      ],
      attributes: {
        exclude: ['warrantyId'],
      },
    });

    return purchaseOrder;
  }

  async getPOListForDelivery(requisitionId) {
    const { PO_STATUS } = this.constants.purchaseOrder;

    const poList = await this.purchaseOrderRepository.findAll({
      attributes: [
        'id',
        [
          this.db.Sequelize.fn(
            'CONCAT',
            'PO-',
            this.db.Sequelize.col('requisition.company_code'),
            this.db.Sequelize.col('po_letter'),
            this.db.Sequelize.col('po_number'),
          ),
          'poNumber',
        ],
      ],
      where: {
        requisitionId,
        status: PO_STATUS.FOR_DELIVERY,
        wasCancelled: false,
        [this.db.Sequelize.Op.and]: [
          this.db.Sequelize.literal(`
            EXISTS (
              SELECT 1 FROM purchase_order_items poi
              WHERE poi.purchase_order_id = "purchase_orders"."id"
              AND poi.quantity_purchased != (
                SELECT COALESCE(SUM(dri.qty_delivered), 0)
                FROM delivery_receipt_items dri
                JOIN delivery_receipts dr ON dri.dr_id = dr.id
                WHERE dri.po_item_id = poi.id
                AND dr.is_draft = false
              )
            )
          `),
        ],
      },
      include: [
        {
          association: 'requisition',
          attributes: [],
        },
      ],
      order: [['id', 'ASC']],
    });

    return poList?.data;
  }

  async getPODetailsForDelivery(purchaseOrderId) {
    const pOrder = await this.purchaseOrderRepository.findOne({
      where: {
        id: purchaseOrderId,
      },
      include: [
        {
          association: 'canvassRequisition',
          attributes: ['id'],
          include: [
            {
              association: 'requisition',
              attributes: ['id', 'companyCode'],
            },
          ],
        },
      ],
    });

    if (!pOrder) {
      throw this.clientErrors.NOT_FOUND({
        message: `Purchase order not found with ID: ${purchaseOrderId}`,
      });
    }

    return {
      id: pOrder.id,
      poNumber: `PO-${pOrder.canvassRequisition.requisition.companyCode}${pOrder.poLetter}${pOrder.poNumber}`,
      supplier: { name: pOrder?.supplierName },
    };
  }

  async getPOListWithNoDeliveryReceipt(requisitionId) {
    const poList = await this.purchaseOrderRepository.findAll({
      attributes: [
        'id',
        [
          this.db.Sequelize.fn(
            'CONCAT',
            'PO-',
            this.db.Sequelize.col('requisition.company_code'),
            this.db.Sequelize.col('po_letter'),
            this.db.Sequelize.col('po_number'),
          ),
          'poNumber',
        ],
      ],
      include: [
        {
          association: 'requisition',
          attributes: ['id', 'companyCode'],
        },
        {
          association: 'deliveryReceipts',

          attributes: ['id'],
          required: false,
        },
      ],
      where: {
        requisitionId,
        status: {
          [this.db.Sequelize.Op.or]: [
            this.constants.purchaseOrder.PO_STATUS.FOR_DELIVERY,
          ],
        },
      },
      order: [['poNumber', 'ASC']],
      subQuery: false,
    });

    return poList?.data;
  }

  async areSomePOItemsPartiallyDelivered(requisitionId) {
    try {
      const purchaseOrderCount = await this.purchaseOrderRepository.count({
        where: {
          requisitionId,
          status: this.constants.purchaseOrder.PO_STATUS.FOR_DELIVERY,
        },
      });

      if (purchaseOrderCount === 0) {
        return false;
      }

      const query = `
        SELECT 
          CASE 
            WHEN EXISTS (
              SELECT 1 FROM purchase_orders 
              WHERE requisition_id = :requisitionId 
              AND status = '${this.constants.purchaseOrder.PO_STATUS.FOR_DELIVERY}'
              AND was_cancelled = false
            ) AND EXISTS (
              SELECT 1
              FROM (
                SELECT 
                  poi.id,
                  poi.quantity_purchased,
                  COALESCE(SUM(dri.qty_delivered), 0) as total_delivered
                FROM 
                  purchase_order_items poi
                JOIN 
                  purchase_orders po ON poi.purchase_order_id = po.id
                LEFT JOIN 
                  delivery_receipt_items dri ON dri.po_item_id = poi.id
                LEFT JOIN 
                  delivery_receipts dr ON dri.dr_id = dr.id AND dr.is_draft = false
                WHERE 
                  po.requisition_id = :requisitionId
                AND 
                  was_cancelled = false
                GROUP BY 
                  poi.id, poi.quantity_purchased
                HAVING 
                  poi.quantity_purchased > COALESCE(SUM(dri.qty_delivered), 0)
              ) as undelivered_items
            ) THEN true
            ELSE false
          END as has_for_delivery_and_undelivered_items`;

      const [results] = await this.db.sequelize.query(query, {
        replacements: { requisitionId },
        type: this.Sequelize.QueryTypes.SELECT,
      });

      return results.has_for_delivery_and_undelivered_items;
    } catch (error) {
      this.fastify.log.error(
        '[ERROR] Checking if all PO items are fully delivered',
      );
      this.fastify.log.error(error);
      throw error;
    }
  }

  async closePurchaseOrder(purchaseOrderId, transaction = null) {
    try {
      const { PO_STATUS } = this.constants.purchaseOrder;

      // Get purchase order details
      const purchaseOrder = await this.purchaseOrderRepository.findOne({
        where: { id: purchaseOrderId },
        attributes: [
          'id',
          'requisitionId',
          'status',
          'poNumber',
          'poLetter',
          'terms',
        ],
        include: [
          {
            association: 'supplier',
            attributes: ['id', 'name'],
            required: false,
          },
          {
            association: 'company',
            attributes: ['id', 'name'],
            required: false,
          },
          {
            association: 'project',
            attributes: ['id', 'name'],
            required: false,
          },
        ],
        raw: false,
      });

      if (!purchaseOrder) {
        throw this.clientErrors.NOT_FOUND({
          message: `Purchase order not found with ID: ${purchaseOrderId}`,
        });
      }

      // Check if PO is in FOR_DELIVERY status
      if (purchaseOrder.status !== PO_STATUS.FOR_DELIVERY) {
        throw this.clientErrors.BAD_REQUEST({
          message: 'Only purchase orders in FOR_DELIVERY status can be closed',
        });
      }

      // Get PO items with their requisition item details
      const poItemsResult = await this.purchaseOrderItemRepository.findAll({
        where: { purchaseOrderId },
        include: [
          {
            association: 'requisitionItemList',
            attributes: ['notes', 'itemType'],
            required: true,
            include: [
              {
                association: 'item',
                required: false,
                attributes: ['id', 'itmDes', 'unit'],
              },
              {
                association: 'nonOfmItem',
                required: false,
                attributes: ['id', 'itemName', 'unit'],
              },
            ],
          },
        ],
        paginate: false,
      });

      if (
        !poItemsResult ||
        !poItemsResult.data ||
        poItemsResult.data.length === 0
      ) {
        this.fastify.log.error(
          `No items found for purchase order ID: ${purchaseOrderId}`,
        );
        throw this.clientErrors.BAD_REQUEST({
          message: `Cannot close purchase order: No items found for purchase order ID: ${purchaseOrderId}`,
        });
      }

      // Process the items to get a consistent format
      const poItems = poItemsResult.data.map((item) => {
        const isOfm = item.requisitionItemList?.item;
        return {
          id: item.id,
          quantityPurchased: item.quantityPurchased,
          itemName: isOfm
            ? item.requisitionItemList.item.itmDes
            : item.requisitionItemList.nonOfmItem?.itemName || 'Unknown Item',
          unit: isOfm
            ? item.requisitionItemList.item.unit
            : item.requisitionItemList.nonOfmItem?.unit || '',
        };
      });

      const deliveryStatus = [];
      const undeliveredItems = [];

      // Check if all items are fully delivered with detailed monitoring
      for (const item of poItems) {
        // Get delivered quantity from delivery receipt items
        const deliveredQuantity = await this.db.sequelize.query(
          `
          SELECT COALESCE(SUM(dri.qty_delivered), 0) as total_delivered
          FROM delivery_receipt_items dri
          JOIN delivery_receipts dr ON dri.dr_id = dr.id
          WHERE dri.po_item_id = :poItemId AND dr.is_draft = false
          `,
          {
            replacements: { poItemId: item.id },
            type: this.Sequelize.QueryTypes.SELECT,
            plain: true,
          },
        );

        // Get item description from delivery receipt items
        const itemDescriptionResult = await this.db.sequelize.query(
          `
          SELECT DISTINCT item_des
          FROM delivery_receipt_items
          WHERE po_item_id = :poItemId
          LIMIT 1
          `,
          {
            replacements: { poItemId: item.id },
            type: this.Sequelize.QueryTypes.SELECT,
            plain: true,
          },
        );

        const purchasedQty = parseFloat(item.quantityPurchased);
        const deliveredQty = parseFloat(deliveredQuantity.total_delivered);

        // Use delivery receipt item description if available, otherwise use the original item name
        const itemName = itemDescriptionResult?.item_des || item.itemName;

        // Track delivery status for each item
        deliveryStatus.push({
          itemId: item.id,
          itemName: itemName,
          quantityPurchased: purchasedQty,
          quantityDelivered: deliveredQty,
          isFullyDelivered: purchasedQty === deliveredQty,
        });

        // Identify items that are not fully delivered
        if (purchasedQty !== deliveredQty) {
          undeliveredItems.push({
            itemId: item.id,
            itemName: itemName,
            quantityPurchased: purchasedQty,
            quantityDelivered: deliveredQty,
            remaining: (purchasedQty - deliveredQty).toFixed(2),
          });
        }
      }

      const purchaseOrderTotals =
        await this.purchaseOrderRepository.getPurchaseOrderTotals(
          purchaseOrderId,
        );

      // Get payment requests separately to verify total amount
      const paymentRequests = await this.db.sequelize.query(
        `
        SELECT id, total_amount, status, created_at
        FROM rs_payment_requests
        WHERE purchase_order_id = :purchaseOrderId
        AND status = 'Closed'
      `,
        {
          replacements: { purchaseOrderId },
          type: this.Sequelize.QueryTypes.SELECT,
          transaction,
        },
      );

      const paymentRequestAmount = paymentRequests.reduce(
        (sum, payment) => sum + parseFloat(payment.total_amount || 0),
        0,
      );

      const purchaseOrderAmount = purchaseOrderTotals.grandTotal;
      const paymentAmount = parseFloat(paymentRequestAmount.toFixed(2));

      // Get supplier name based on supplier type
      let supplierName = 'N/A';
      if (purchaseOrder.supplier) {
        supplierName = purchaseOrder.supplier.name;
      } else if (purchaseOrder.company) {
        supplierName = purchaseOrder.company.name;
      } else if (purchaseOrder.project) {
        supplierName = purchaseOrder.project.name;
      }

      // Verify that total payment amount does not exceed purchase order amount
      if (paymentAmount > purchaseOrderAmount) {
        throw this.clientErrors.BAD_REQUEST({
          message: `Cannot close purchase order: Total payment amount (${paymentAmount}) exceeds purchase order amount (${purchaseOrderAmount})`,
          description: {
            metadata: {
              purchaseOrderId: purchaseOrder.id,
              poNumber: `PO-${purchaseOrder.poLetter}${purchaseOrder.poNumber}`,
              supplier: supplierName,
              terms: purchaseOrder.terms,
              purchaseOrderAmount,
              paymentRequestAmount: paymentAmount,
              difference: (paymentAmount - purchaseOrderAmount).toFixed(2),
              paymentRequests: paymentRequests.map((pr) => ({
                id: pr.id,
                amount: parseFloat(pr.total_amount || 0),
                status: pr.status,
                createdAt: pr.created_at,
              })),
              deliveryStatus: {
                totalItems: poItems.length,
                fullyDeliveredItems: poItems.length - undeliveredItems.length,
                itemDetails: deliveryStatus,
              },
            },
          },
        });
      } else if (paymentAmount < purchaseOrderAmount) {
        return;
      }

      // Update PO status to CLOSED_PO
      await this.purchaseOrderRepository.update(
        { id: purchaseOrderId },
        { status: PO_STATUS.CLOSED_PO },
        { transaction },
      );

      // Check if all purchase orders for this requisition are closed
      const openPOsCount = await this.purchaseOrderRepository.count({
        where: {
          requisitionId: purchaseOrder.requisitionId,
          status: { [this.Sequelize.Op.ne]: PO_STATUS.CLOSED_PO },
          wasCancelled: false,
        },
        transaction,
      });

      // If no open POs remain, check for uncanvassed items before closing requisition
      if (openPOsCount === 0) {
        // Use requisitionService to handle requisition closure logic
        await this.requisitionService.closeRequisition(
          purchaseOrder.requisitionId,
          transaction,
        );
      }

      return {
        success: true,
        message: 'Purchase order closed successfully',
        metadata: {
          purchaseOrderId: purchaseOrder.id,
          poNumber: `PO-${purchaseOrder.poLetter}${purchaseOrder.poNumber}`,
          supplier: supplierName,
          terms: purchaseOrder.terms,
          purchaseOrderAmount: purchaseOrderAmount,
          paymentRequestAmount: paymentAmount,
          paymentRequests: paymentRequests.map((pr) => ({
            id: pr.id,
            amount: parseFloat(pr.total_amount || 0),
            status: pr.status,
            createdAt: pr.created_at,
          })),
          deliveryStatus: {
            totalItems: poItems.length,
            fullyDeliveredItems: poItems.length,
            itemDetails: deliveryStatus,
          },
        },
      };
    } catch (error) {
      this.fastify.log.error('[ERROR] Closing purchase order');
      this.fastify.log.error(error);
      throw error;
    }
  }

  async getSupplierDetails(purchaseOrderId) {
    const purchaseOrder = await this.purchaseOrderRepository.findOne({
      where: {
        id: purchaseOrderId,
      },
      attributes: ['id', 'supplierId', 'supplierType'],
    });

    if (!purchaseOrder) {
      throw this.clientErrors.NOT_FOUND({
        message: `Purchase order not found with ID: ${purchaseOrderId}`,
      });
    }

    let supplier;
    if (purchaseOrder.supplierType === 'supplier') {
      supplier = await this.supplierRepository.findOne({
        attributes: ['id', 'name', 'tin'],
        where: {
          id: purchaseOrder.supplierId,
        },
      });
    } else if (purchaseOrder.supplierType === 'company') {
      supplier = await this.companyRepository.findOne({
        attributes: ['id', 'name', 'tin'],
        where: {
          id: purchaseOrder.supplierId,
        },
      });
    }

    return supplier;
  }

  async generatePurchaseOrderPdf(purchaseOrderId) {
    // Get Purchase Order details
    const purchaseOrder = await this.purchaseOrderRepository.findOne({
      where: { id: purchaseOrderId },
      attributes: [
        'id',
        'poLetter',
        'poNumber',
        'totalDiscountedAmount',
        'terms',
        'isNewDeliveryAddress',
        'newDeliveryAddress',
        'supplierName',
        'supplierType',
        'supplierId',
        'supplierDetails',
      ],
      include: [
        {
          association: 'requisition',
          attributes: ['companyCode', 'deliveryAddress', 'dateRequired'],
          include: [
            {
              association: 'company',
              attributes: ['name', 'tin'],
            },
            {
              association: 'project',
              attributes: ['name'],
              include: [
                {
                  association: 'company',
                  attributes: ['id', 'contactNumber'],
                },
              ],
            },
            {
              association: 'assignee',
            },
          ],
        },
        {
          association: 'supplier',
          attributes: [
            'name',
            'tin',
            'contactPerson',
            'contactNumber',
            'address',
          ],
        },
        {
          association: 'project',
          attributes: ['name', 'initial', 'address'],
          include: [
            {
              association: 'company',
              attributes: ['id', 'name', 'contactNumber', 'tin', 'address'],
            },
          ],
        },
        {
          association: 'company',
          attributes: ['name', 'contactNumber', 'tin', 'address'],
        },
        {
          association: 'warranty',
          attributes: ['name'],
        },
        {
          association: 'purchaseOrderApprovers',
          attributes: ['id'],
          include: [
            {
              association: 'approver',
              attributes: ['firstName', 'lastName'],
            },
            {
              association: 'role',
              attributes: ['name'],
            },
          ],
        },
      ],
    });

    if (!purchaseOrder) {
      throw this.clientErrors.NOT_FOUND({
        message: `Purchase Order with ID ${purchaseOrderId} not found`,
      });
    }

    // Get PO items with correct associations
    const poItems = await this.purchaseOrderItemRepository.findAll({
      where: {
        purchaseOrderId,
        [this.Sequelize.Op.or]: [
          { '$requisitionItemList.item.id$': { [this.Sequelize.Op.ne]: null } },
          {
            '$requisitionItemList.nonOfmItem.id$': {
              [this.Sequelize.Op.ne]: null,
            },
          },
        ],
      },
      include: [
        {
          association: 'canvassItemSupplier',
          include: [
            {
              association: 'canvassItem',
            },
          ],
        },
        {
          association: 'requisitionItemList',
          include: [
            {
              association: 'item',
              required: false,
            },
            {
              association: 'nonOfmItem',
              required: false,
            },
          ],
        },
      ],
      order: [
        ['requisitionItemList', 'item', 'isSteelbars', 'ASC'],
        [
          this.Sequelize.literal(`
            COALESCE(
            "requisitionItemList->item"."itm_des",
            "requisitionItemList->nonOfmItem"."item_name"
            )
            `),
          'ASC',
        ],
      ],
      paginate: false,
    });

    if (!poItems || !poItems.data || !poItems.data.length) {
      throw this.clientErrors.BAD_REQUEST({
        message: 'No items found for this Purchase Order',
      });
    }

    // Get PO summary details for grandTotal
    const poSummary = await this.purchaseOrderItemRepository.getPOItemsSummary({
      purchaseOrderId,
    });

    // Format date in the required format (e.g., "12 MAY 2025")
    const formatDateToUpperCase = (date) => {
      if (!date) return '---';
      const d = new Date(date);
      const months = [
        'JAN',
        'FEB',
        'MAR',
        'APR',
        'MAY',
        'JUN',
        'JUL',
        'AUG',
        'SEP',
        'OCT',
        'NOV',
        'DEC',
      ];
      return `${d.getDate()} ${months[d.getMonth()]} ${d.getFullYear()}`;
    };

    // Helper function to format numbers since formatNumber is not available
    const formatNumber = (value, decimals = 2) => {
      if (value === null || value === undefined) return '0.00';
      return parseFloat(value)
        .toFixed(decimals)
        .replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    };

    const formattedItems = poItems.data.map((item, index) => {
      // Get item name and unit based on item type
      let itemName = 'N/A';
      let unit = '---';
      let notes = '';

      if (item.requisitionItemList) {
        notes = item.requisitionItemList.notes || '';

        if (
          item.requisitionItemList.itemType === 'ofm' ||
          item.requisitionItemList.itemType === 'ofm-tom'
        ) {
          itemName = item.requisitionItemList.item.itmDes || '---';
          unit = item.requisitionItemList.item.unit || '---';
        } else {
          itemName = item.requisitionItemList.nonOfmItem.itemName || '---';
          unit =
            item.requisitionItemList.item?.unit ||
            item.requisitionItemList.nonOfmItem?.unit;
        }
      }

      // Get price information from canvassItemSupplier
      const unitPrice = item.canvassItemSupplier?.unitPrice || 0;
      const discount =
        parseFloat(item.canvassItemSupplier?.discountValue) *
          item.quantityPurchased || 0;
      const totalPrice = item.quantityPurchased * unitPrice - discount;

      return {
        id: index + 1,
        itemName: itemName,
        latestNote: notes || `PO-${purchaseOrder.poNumber}`,
        requestedQty: item.quantityPurchased,
        unit: unit,
        unitPrice: `₱${formatNumber(unitPrice)}`,
        discount: `₱${formatNumber(discount)}`,
        totalPrice: `₱${formatNumber(totalPrice)}`,
      };
    });
    // Paginate items
    const { pagesData, totalPages, totalItems } =
      this.utils.paginateItemsWithDifferentSizes(formattedItems, 21, 21);

    // Get company and supplier details
    const company = purchaseOrder.requisition?.company;
    const project = purchaseOrder.requisition?.project;

    // Generate PO number
    const poNumber = `PO-${purchaseOrder.requisition?.companyCode || ''}${purchaseOrder.poLetter}${purchaseOrder.poNumber}`;

    // Find approvers by role
    let supervisorApprover = '---';
    let purchasingHeadApprover = '---';

    if (
      purchaseOrder.purchaseOrderApprovers &&
      purchaseOrder.purchaseOrderApprovers.length > 0
    ) {
      // Find supervisor approver
      const supervisor = purchaseOrder.purchaseOrderApprovers.find(
        (approver) =>
          approver.role &&
          approver.role.name === 'Supervisor' &&
          approver.approver,
      );

      if (supervisor && supervisor.approver) {
        supervisorApprover =
          `${supervisor.approver.firstName || ''} ${supervisor.approver.lastName || ''}`.trim();
      }

      // Find purchasing head approver
      const purchasingHead = purchaseOrder.purchaseOrderApprovers.find(
        (approver) =>
          approver.role &&
          approver.role.name === 'Purchasing Head' &&
          approver.approver,
      );

      if (purchasingHead && purchasingHead.approver) {
        purchasingHeadApprover =
          `${purchasingHead.approver.firstName || ''} ${purchasingHead.approver.lastName || ''}`.trim();
      }
    }

    // Vendor rework
    const vendor = {
      vendorName:
        purchaseOrder.supplierDetails?.name ||
        purchaseOrder.supplierName ||
        '---',
      vendorTin: purchaseOrder.supplierDetails?.tin || '---',
      vendorContactPerson:
        purchaseOrder.supplierDetails?.contactPerson || '---',
      vendorContactNumber:
        purchaseOrder.supplierDetails?.contactNumber || '---',
      vendorAddress: purchaseOrder.supplierDetails?.address || '---',
    };

    let contact = '';

    if (vendor.vendorContactPerson && vendor.vendorContactNumber) {
      contact = `${vendor.vendorContactPerson} / ${vendor.vendorContactNumber}`;
    } else if (vendor.vendorContactPerson) {
      contact = vendor.vendorContactPerson;
    } else {
      contact = vendor.vendorContactNumber;
    }

    // Prepare data in the exact format required
    const poData = {
      poNumber: poNumber,
      dateExtracted: formatDateToUpperCase(new Date()),
      projectName: project?.name || company?.name || '---',
      tin: company?.tin || '---',
      businessStyle: project?.name || company?.name || '---',
      companyName: vendor.vendorName,
      vendorTin: vendor.vendorTin,
      contactName: contact,
      contactNumber: '',
      streetAddress: vendor.vendorAddress,
      deliverToStreetAddress: purchaseOrder.isNewDeliveryAddress
        ? purchaseOrder.newDeliveryAddress
        : purchaseOrder.requisition?.deliveryAddress || '---',
      deliverToContactPerson: project?.company.contactNumber || '---',

      termsOfPayment: purchaseOrder.terms || '---',
      deliveryTime: formatDateToUpperCase(
        purchaseOrder.requisition?.dateRequired,
      ),
      warranty: purchaseOrder.warranty?.name || '---',
      otherSpecifications: '',

      purchasingStaffName:
        purchaseOrder.requisition?.assignee?.fullNameUser || '---',
      notedBy: supervisorApprover,
      approvedBy: purchasingHeadApprover,

      totalPages,
      totalItems,
      pagesData,
      totalDiscount: `₱${formatNumber(poSummary.discount)}`,
      grandTotal: `₱${formatNumber(poSummary.totalAmount)}`,
      amountInWords: this.utils.amountInWordsPHP(poSummary.totalAmount),
    };

    return {
      templateData: poData,
      fileName: 'PO',
    };
  }

  async getPurchaseOrderAgainstPaymentRequestMonitoring(purchaseOrderId) {
    // Validate purchase order exists
    const purchaseOrder = await this.purchaseOrderRepository.findOne({
      where: { id: purchaseOrderId },
      attributes: [
        'id',
        'poNumber',
        'poLetter',
        'supplierName',
        'supplierId',
        'supplierType',
        'supplierDetails',
        'withholdingTaxDeduction',
        'deliveryFee',
        'tip',
        'extraCharges',
      ],
      include: [
        {
          association: 'supplier',
          attributes: [
            'name',
            'tin',
            'contactPerson',
            'contactNumber',
            'address',
          ],
        },
        {
          association: 'project',
          attributes: ['name', 'initial', 'address'],
        },
        {
          association: 'company',
          attributes: ['name', 'contactNumber', 'tin', 'address'],
        },
      ],
    });

    const amountSummary =
      await this.purchaseOrderItemRepository.getPOItemsSummary({
        purchaseOrderId,
      });

    if (!purchaseOrder) {
      throw this.clientErrors.NOT_FOUND({
        message: `Purchase Order with ID: ${purchaseOrderId} not found`,
      });
    }

    // Calculate totals
    const poAmount =
      parseFloat(amountSummary.totalAmount || 0) -
      parseFloat(purchaseOrder.withholdingTaxDeduction || 0) +
      parseFloat(purchaseOrder.deliveryFee || 0) +
      parseFloat(purchaseOrder.tip || 0) +
      parseFloat(purchaseOrder.extraCharges || 0);

    const invoices = await this.invoiceReportRepository.findAll({
      where: { purchaseOrderId },
      attributes: ['id', 'invoiceAmount', 'status'],
      paginate: false,
    });

    const invoiceTotal = invoices.data.reduce((sum, invoice) => {
      // Only include payment requests with status 'Closed'
      if (invoice.status === 'Invoice Received') {
        return sum + parseFloat(invoice.invoiceAmount || 0);
      }
      return sum;
    }, 0);

    const remainingTotalInvoiceAmount = poAmount - invoiceTotal;

    // Get all payment requests for this PO
    const paymentRequests = await this.rsPaymentRequestRepository.findAll({
      where: { purchaseOrderId },
      attributes: [
        'id',
        'prNumber',
        'prLetter',
        'totalAmount',
        'status',
        'createdAt',
        'delivery_invoice_id',
      ],
      paginate: false,
    });

    const paymentRequestsTotal = paymentRequests.data.reduce((sum, pr) => {
      // Only include payment requests with status 'Closed'
      if (
        pr.status ===
        this.constants.rsPaymentRequest.RS_PAYMENT_REQUEST_STATUS.APPROVED
      ) {
        return sum + parseFloat(pr.totalAmount || 0);
      }
      return sum;
    }, 0);

    const remainingAmount = poAmount - paymentRequestsTotal;

    return {
      purchaseOrder: {
        id: purchaseOrder.id,
        poNumber: `PO-${purchaseOrder.poLetter}${purchaseOrder.poNumber}`,
        totalAmount: poAmount.toFixed(2),
        supplier:
          purchaseOrder.supplierDetails?.name ||
          purchaseOrder?.supplierName ||
          'N/A',
      },
      paymentRequests: paymentRequests.data.map((pr) => ({
        id: pr.id,
        prNumber: `PR-${pr.prLetter}${pr.prNumber}`,
        amount: parseFloat(pr.totalAmount || 0).toFixed(2),
        status: pr.status,
        createdAt: pr.createdAt,
      })),
      summary: {
        purchaseOrderAmount: poAmount.toFixed(2),
        closedPaymentRequestsTotal: paymentRequestsTotal.toFixed(2),
        remainingAmountToPay: remainingAmount.toFixed(2),
        remainingTotalInvoiceAmount: remainingTotalInvoiceAmount.toFixed(2),
        isComplete: Math.abs(remainingAmount) < 0.01, // Allow for small rounding differences
        paymentRequestCount: paymentRequests.total,
      },
    };
  }

  async addAdditionalFees(
    purchaseOrderId,
    { withholdingTaxDeduction, deliveryFee, tip, extraCharges },
    userFromToken,
    transaction,
  ) {
    const { PO_STATUS } = this.constants.purchaseOrder;
    const { RS_PAYMENT_REQUEST_STATUS } = this.constants.rsPaymentRequest;

    // Get the purchase order
    const existingPurchaseOrder =
      await this.purchaseOrderRepository.getById(purchaseOrderId);

    if (!existingPurchaseOrder) {
      throw this.clientErrors.NOT_FOUND({
        message: `Purchase Order with ID: ${purchaseOrderId} not found`,
      });
    }

    // Check if PO is in a valid status for adding fees
    const validStatuses = [
      PO_STATUS.FOR_SENDING,
      PO_STATUS.FOR_DELIVERY,
      PO_STATUS.PO_REJECTED,
    ];
    if (!validStatuses.includes(existingPurchaseOrder.status)) {
      throw this.clientErrors.BAD_REQUEST({
        message: `Cannot update additional fees due to invalid status.`,
      });
    }

    // Check if user is the assigned purchasing staff
    if (existingPurchaseOrder.assignedTo !== userFromToken.id) {
      throw this.clientErrors.FORBIDDEN({
        message: `Only the assigned purchasing staff can add additional fees`,
      });
    }

    // Check if incidental fees are already used in a payment request
    const paymentRequests = await this.rsPaymentRequestRepository.findAll({
      where: {
        purchaseOrderId,
        includesIncidentalFees: true,
        status: { [this.Sequelize.Op.ne]: RS_PAYMENT_REQUEST_STATUS.REJECTED },
      },
      paginate: false,
    });

    if (paymentRequests.total > 0) {
      throw this.clientErrors.BAD_REQUEST({
        message: `Cannot modify additional fees. They are already included in a submitted payment request.`,
      });
    }

    if (existingPurchaseOrder.status === PO_STATUS.FOR_DELIVERY) {
      // Validate PO has required documents
      const [deliveryReceipts, invoices, paymentRequests] = await Promise.all([
        this.deliveryReceiptRepository.findAll({
          where: { poId: purchaseOrderId, isDraft: false },
          paginate: false,
        }),
        this.invoiceReportRepository.findAll({
          where: { purchaseOrderId, isDraft: false },
          paginate: false,
        }),
        this.rsPaymentRequestRepository.findAll({
          where: { purchaseOrderId, isDraft: false },
          paginate: false,
        }),
      ]);

      if (
        deliveryReceipts.total === 0 ||
        invoices.total === 0 ||
        paymentRequests.total === 0
      ) {
        throw this.clientErrors.BAD_REQUEST({
          message:
            'Purchase order must have at least one delivery receipt, invoice report, and payment request before adding fees once purchase order is For Delivery.',
        });
      }
    }

    // Validate total amount first with created payment requests
    // Get all payment requests and their associated invoice amounts
    const paymentRequestsWithInvoices =
      await this.rsPaymentRequestRepository.findAll({
        where: {
          purchaseOrderId,
          isDraft: false,
        },
        include: [
          {
            model: this.db.invoiceReportModel,
            as: 'invoiceReports',
            attributes: ['invoiceAmount'],
          },
        ],
        paginate: false,
      });

    // Calculate total amount from payment requests based on their invoice amounts
    const sum = paymentRequestsWithInvoices.data.reduce((total, pr) => {
      const prInvoiceTotal =
        pr.invoiceReports?.reduce(
          (sum, invoice) => sum + parseFloat(invoice.invoiceAmount || 0),
          0,
        ) || 0;
      return total + prInvoiceTotal;
    }, 0);

    const isValidTotalAmount =
      Number(sum) < Number(existingPurchaseOrder.totalAmount);

    if (!isValidTotalAmount) {
      throw this.clientErrors.BAD_REQUEST({
        message: `Total payment request amount exceeds purchase order amount after adding additional fees.`,
      });
    }

    // Add additional fees
    await this.purchaseOrderRepository.update(
      { id: purchaseOrderId },
      {
        withholdingTaxDeduction,
        deliveryFee,
        tip,
        extraCharges,
      },
      { transaction },
    );

    // Add note
    await this.noteService.createNote(
      {
        model: this.constants.note.MODELS.PURCHASE_ORDER,
        modelId: Number(purchaseOrderId),
        userName: userFromToken.fullNameUser,
        userType: this.constants.note.USER_TYPES.REQUESTOR,
        commentType: this.constants.note.COMMENT_TYPES.NOTE,
        note: `Additional fees have been updated to the purchase order.`,
      },
      {
        transaction,
      },
    );

    // Get approvers user IDs
    const approvers = await this.purchaseOrderApproverRepository.findAll({
      where: { purchaseOrderId },
      attributes: ['userId'],
      paginate: false,
    });

    const approverUserIds = approvers.data.map((approver) => approver.userId);

    // Send notification to approvers
    await this.notificationService.sendNotification({
      transaction,
      senderId: userFromToken.id,
      type: this.constants.notification.NOTIFICATION_TYPES.PURCHASE_ORDER,
      title: 'Purchase Order has been updated with Additional Fees',
      message: `Assigned Purchasing Staff for PO-${existingPurchaseOrder.poLetter}${existingPurchaseOrder.poNumber} has added an Additional Fees to the Purchase Order. Click here or access the Dashboard to proceed in reviewing the Purchase Order.`,
      recipientUserIds: approverUserIds,
      metaData: {
        purchaseOrderId,
        requisitionId: existingPurchaseOrder.requisitionId,
      },
    });

    return true;
  }

  async resetToForApproval(purchaseOrderId, transaction) {
    await this.purchaseOrderRepository.update(
      { id: purchaseOrderId },
      { status: this.constants.purchaseOrder.PO_STATUS.FOR_PO_APPROVAL },
      { transaction },
    );

    // Check if all approvers have approved
    const approvers = await this.purchaseOrderApproverRepository.findAll({
      where: { purchaseOrderId },
      attributes: ['status'],
      paginate: false,
    });

    const allApproved = approvers.data.every(
      (approver) =>
        approver.status ===
        this.constants.purchaseOrder.PO_APPROVER_STATUS.APPROVED,
    );

    if (allApproved) {
      // Update all to PENDING
      await this.purchaseOrderApproverRepository.update(
        { purchaseOrderId },
        { status: this.constants.purchaseOrder.PO_APPROVER_STATUS.PENDING },
        { transaction },
      );
    } else {
      // Update only REJECTED to PENDING
      await this.purchaseOrderApproverRepository.update(
        {
          purchaseOrderId,
          status: this.constants.purchaseOrder.PO_APPROVER_STATUS.REJECTED,
        },
        { status: this.constants.purchaseOrder.PO_APPROVER_STATUS.PENDING },
        { transaction },
      );
    }

    return;
  }
}

module.exports = PurchaseOrderService;
