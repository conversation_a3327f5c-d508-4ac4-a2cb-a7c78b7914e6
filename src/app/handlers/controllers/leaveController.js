class Leave {
  constructor(container) {
    const {
      db,
      utils,
      entities,
      leaveService,
      fastify,
      leaveRepository,
      clientErrors,
    } = container;
    this.fastify = fastify;
    this.db = db;
    this.utils = utils;
    this.entities = entities;
    this.leaveService = leaveService;
    this.leaveRepository = leaveRepository;
    this.clientErrors = clientErrors;
  }

  async getAllLeaves(request, reply) {
    const result = await this.leaveService.getAllLeaves(request);
    return reply.status(200).send({
      message: `Successfully Retrieved all leaves.`,
      result,
    });
  }

  async getAllLeavesByUserId(request, reply) {
    const result = await this.leaveService.getAllLeavesByUserId(request);
    return reply.status(200).send({
      message: `Successfully Retrieved all leaves of User with ID ${request.userFromToken.id}`,
      result,
    });
  }

  async createLeave(request, reply) {
    const { userFromToken, body } = request;

    try {
      const leave = await this.leaveService.createLeave({
        ...userFromToken,
        ...body,
      });

      return reply.status(200).send({
        message: `Successfully Created Leave Case for User ID of ${userFromToken.id}`,
        result: leave,
      });
    } catch (error) {
      this.fastify.log.info(
        `Failed Creating Leave Case for User ID of ${userFromToken.id}`,
      );
      throw error;
    }
  }

  async getLeaveByPk(request, reply) {
    const { id } = request.params;

    const leave = await this.leaveService.getLeaveByPk(id);

    if (!leave) {
      throw this.clientErrors.NOT_FOUND({
        message: `Leave not found with ID of ${id}`,
      });
    }
    return reply.status(200).send({
      message: `Successfully Retrieved Leave Case of ID ${id}`,
      result: leave,
    });
  }

  async getWorkflowsByUserId(request, reply) {
    const { userId } = request.params;

    const workflow = await this.leaveService.getWorkflowByUserId(userId);

    if (!workflow) {
      throw this.clientErrors.NOT_FOUND({
        message: `Workflow not found with user ID of ${userId}`,
      });
    }
    return reply.status(200).send({
      message: `Successfully Retrieved Workflow of user ID: ${userId}`,
      result: workflow,
    });
  }

  async updateLeave(request, reply) {
    const { body, params, userFromToken } = request;
    const { startDate, endDate } = body;
    const newStartDate = new Date(startDate);
    const newEndDate = new Date(endDate);
    const { id } = params;

    const leave = await this.leaveRepository.getById(id);

    if (newEndDate < newStartDate) {
      throw this.clientErrors.BAD_REQUEST({
        message: 'End Date cannot be earlier than start date.',
      });
    }

    if (!leave) {
      throw this.clientErrors.NOT_FOUND({ message: 'Leave not found' });
    }

    try {
      const updated = await this.leaveService.updateLeave({
        body,
        id,
        userFromToken,
      });

      return reply.status(200).send({
        message: `Successfully Updated Leave Case of ID ${id}`,
        result: updated,
        previousValue: leave,
      });
    } catch (error) {
      this.fastify.log.info(`Failed Updating Leave Case of ID ${id}`);
      throw error;
    }
  }

  async deleteLeave(request, reply) {
    const { params, userFromToken } = request;
    const { id } = params;
    const transaction = await this.db.sequelize.transaction();

    this.fastify.log.info(`Trying to Delete Leave Case of ID ${id}...`);

    const leave = await this.leaveRepository.getById(id);

    if (!leave) {
      throw this.clientErrors.NOT_FOUND({ message: 'Leave Case not found' });
    }

    try {
      const result = await this.leaveService.deleteLeave({
        id,
        userFromToken,
      });
      await transaction.commit();
      return reply.status(200).send({
        message: `Successfully Deleted Leave Case of ID ${id}`,
        result,
      });
    } catch (error) {
      this.fastify.log.info(`Failed Deleting Leave Case of ID ${id}`);
      await transaction.rollback();
      throw error;
    }
  }

  async assignApprover(request, reply) {
    const transaction = await this.db.sequelize.transaction();
    try {
      this.fastify.log.info(`Adding Alternate Approver...`);
      const result = await this.leaveService.assignApprover({
        ...request.body,
        userFromToken: request.userFromToken,
      });
      this.fastify.log.info(`Successfully added Alternate Approver...`);
      await transaction.commit();

      return reply.status(200).send({
        message: `Successfully Added Alternate Approver`,
        result,
      });
    } catch (error) {
      this.fastify.log.info(`Failed Adding Alternate Approver`);
      this.fastify.log.info(`Rollback in progress...`);
      await transaction.rollback();
      this.fastify.log.info(`Rollback is done!`);
      throw error;
    }
  }


  async addAltApprover(request, reply) {
    const transaction = await this.db.sequelize.transaction();
    const { leaveId } = request.params;
    try {
      this.fastify.log.info(`Adding Alternate Approver...`);
      const result = await this.leaveService.addAltApprover({
        ...request.body,
        userFromToken: request.userFromToken,
        leaveId: leaveId? leaveId : null,
      });
      this.fastify.log.info(`Successfully added Alternate Approver...`);
      await transaction.commit();

      return reply.status(200).send({
        message: `Successfully Added Alternate Approver`,
        result,
      });
    } catch (error) {
      this.fastify.log.info(`Failed Adding Alternate Approver`);
      this.fastify.log.info(`Rollback in progress...`);
      await transaction.rollback();
      this.fastify.log.info(`Rollback is done!`);
      throw error;
    }
  }
}

module.exports = Leave;
